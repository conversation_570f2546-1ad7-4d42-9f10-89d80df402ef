import { assessmentParser } from '../utils/assessmentParser.js';
import { assessmentScoring } from '../utils/assessmentScoring.js';
import { assessmentService } from '../services/assessmentService.js';

// Assessment state management
let currentPhase = 1;
let currentQuestionIndex = 0;
let phaseQuestions = {};
let answers = {};

export function createAssessment3PhasePage() {
  return `
    <div class="min-h-screen bg-gray-50 flex">
      <!-- Main Content -->
      <div class="flex-1 flex flex-col">
        <!-- Top Navigation -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
          <div class="px-6 py-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <button onclick="navigateTo('dashboard')" class="text-gray-500 hover:text-gray-700">
                  ← Kembali ke Dashboard
                </button>
                <div class="h-6 w-px bg-gray-300"></div>
                <h1 class="text-xl font-semibold text-gray-900" id="phase-title">Phase 1: Big Five Personality</h1>
              </div>
              <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-500" id="question-counter">Pertanyaan 1 dari 44</span>
                <button onclick="saveAndExit()" class="text-sm text-gray-500 hover:text-gray-700">
                  Simpan & Keluar
                </button>
              </div>
            </div>
          </div>
        </nav>

        <!-- Phase Progress Bar -->
        <div class="bg-white border-b border-gray-200">
          <div class="px-6 py-3">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-700" id="phase-progress-label">Phase 1 Progress</span>
              <span class="text-sm text-gray-500" id="phase-progress-text">0%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div id="phase-progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
          </div>
        </div>

        <!-- Assessment Content -->
        <div class="flex-1 p-6">
          <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
              <!-- Question Content -->
              <div id="question-content">
                <div class="mb-6">
                  <div class="flex items-center mb-4">
                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full" id="question-category">
                      Openness to Experience
                    </span>
                  </div>
                  <h2 class="text-xl font-semibold text-gray-900 mb-3" id="question-text">
                    Saya adalah seseorang yang original, comes up with new ideas
                  </h2>
                  <p class="text-gray-600 text-sm mb-6">
                    Pilih seberapa setuju Anda dengan pernyataan di atas menggunakan skala 1-7
                  </p>
                </div>

                <!-- Likert Scale -->
                <div class="mb-8">
                  <div class="grid grid-cols-7 gap-2 mb-4">
                    <div class="text-center">
                      <div class="text-xs text-gray-500 mb-2">Sangat Tidak Setuju</div>
                      <div class="text-lg font-bold text-red-600">1</div>
                    </div>
                    <div class="text-center">
                      <div class="text-xs text-gray-500 mb-2">Tidak Setuju</div>
                      <div class="text-lg font-bold text-red-400">2</div>
                    </div>
                    <div class="text-center">
                      <div class="text-xs text-gray-500 mb-2">Agak Tidak Setuju</div>
                      <div class="text-lg font-bold text-orange-400">3</div>
                    </div>
                    <div class="text-center">
                      <div class="text-xs text-gray-500 mb-2">Netral</div>
                      <div class="text-lg font-bold text-gray-500">4</div>
                    </div>
                    <div class="text-center">
                      <div class="text-xs text-gray-500 mb-2">Agak Setuju</div>
                      <div class="text-lg font-bold text-green-400">5</div>
                    </div>
                    <div class="text-center">
                      <div class="text-xs text-gray-500 mb-2">Setuju</div>
                      <div class="text-lg font-bold text-green-500">6</div>
                    </div>
                    <div class="text-center">
                      <div class="text-xs text-gray-500 mb-2">Sangat Setuju</div>
                      <div class="text-lg font-bold text-green-600">7</div>
                    </div>
                  </div>

                  <!-- Radio buttons -->
                  <div class="grid grid-cols-7 gap-2" id="likert-options">
                    ${Array.from({length: 7}, (_, i) => `
                      <div class="flex justify-center">
                        <input type="radio" name="answer" value="${i + 1}" 
                               class="w-6 h-6 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                               onchange="handleAnswerChange(${i + 1})">
                      </div>
                    `).join('')}
                  </div>
                </div>
              </div>

              <!-- Navigation Buttons -->
              <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                <button onclick="previousQuestion()" 
                        id="prev-btn" 
                        class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled>
                  ← Sebelumnya
                </button>
                
                <div class="flex space-x-3">
                  <button onclick="skipQuestion()" 
                          class="px-4 py-2 text-gray-500 hover:text-gray-700">
                    Lewati
                  </button>
                  <button onclick="nextQuestion()" 
                          id="next-btn"
                          class="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed">
                    Selanjutnya →
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Sidebar -->
      <div class="w-80 bg-white shadow-lg border-l border-gray-200">
        <div class="h-full flex flex-col">
          <!-- Sidebar Header -->
          <div class="p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Assessment Progress</h2>
          </div>

          <!-- Phase Navigation -->
          <div class="flex-1 overflow-y-auto p-4">
            <div id="phase-navigation">
              <!-- Phase navigation will be populated here -->
            </div>
          </div>

          <!-- Total Progress -->
          <div class="p-4 border-t border-gray-200">
            <div class="text-sm text-gray-500 text-center">
              <div class="mb-2 font-medium">Total Progress</div>
              <div class="w-full bg-gray-200 rounded-full h-3 mb-2">
                <div id="total-progress" class="bg-indigo-600 h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
              </div>
              <div id="total-progress-text" class="text-xs font-medium">0% Complete</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

// Initialize the 3-phase assessment
export function initAssessment3Phase() {
  // Load all phase questions
  phaseQuestions[1] = assessmentParser.getPhaseQuestions(1);
  phaseQuestions[2] = assessmentParser.getPhaseQuestions(2);
  phaseQuestions[3] = assessmentParser.getPhaseQuestions(3);
  
  // Initialize state
  currentPhase = 1;
  currentQuestionIndex = 0;
  answers = JSON.parse(localStorage.getItem('assessment3PhaseAnswers') || '{}');
  
  // Build sidebar navigation
  buildSidebarNavigation();
  
  // Load first question
  loadCurrentQuestion();
  
  // Update progress
  updateProgress();
}

function buildSidebarNavigation() {
  const container = document.getElementById('phase-navigation');
  if (!container) return;
  
  const phases = assessmentParser.getAllPhases();
  let html = '';
  
  Object.keys(phases).forEach(phaseNum => {
    const phase = phases[phaseNum];
    const questions = phaseQuestions[phaseNum];
    const phaseAnswers = Object.keys(answers).filter(key => 
      questions.some(q => q.id.toString() === key)
    ).length;
    
    html += `
      <div class="mb-4">
        <button onclick="jumpToPhase(${phaseNum})" 
                class="w-full text-left p-3 rounded-lg border transition-colors ${currentPhase == phaseNum ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:bg-gray-50'}">
          <div class="flex items-center justify-between mb-2">
            <div class="font-medium text-gray-900">Phase ${phaseNum}</div>
            <div class="text-sm font-medium text-gray-600">${phaseAnswers}/${questions.length}</div>
          </div>
          <div class="text-sm text-gray-500 mb-2">${phase.name}</div>
          <div class="w-full bg-gray-200 rounded-full h-1.5">
            <div class="bg-indigo-600 h-1.5 rounded-full transition-all duration-300" 
                 style="width: ${Math.round((phaseAnswers / questions.length) * 100)}%"></div>
          </div>
        </button>
        
        ${currentPhase == phaseNum ? `
          <div class="mt-2 space-y-1">
            ${phase.categories.map((category, index) => {
              const categoryQuestions = questions.filter(q => q.category === category);
              const categoryAnswers = Object.keys(answers).filter(key => 
                categoryQuestions.some(q => q.id.toString() === key)
              ).length;
              
              return `
                <button onclick="jumpToCategory('${category}')" 
                        class="w-full text-left p-2 text-sm rounded transition-colors ${getCurrentQuestion()?.category === category ? 'bg-blue-100 text-blue-700 border border-blue-200' : 'text-gray-600 hover:bg-gray-50'}">
                  <div class="flex justify-between items-center">
                    <span class="font-medium">${category}</span>
                    <span class="text-xs px-2 py-0.5 rounded-full ${categoryAnswers === categoryQuestions.length ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-500'}">${categoryAnswers}/${categoryQuestions.length}</span>
                  </div>
                </button>
              `;
            }).join('')}
          </div>
        ` : ''}
      </div>
    `;
  });
  
  container.innerHTML = html;
}

function loadCurrentQuestion() {
  const question = getCurrentQuestion();
  if (!question) return;
  
  // Update question display
  document.getElementById('question-text').textContent = question.text;
  document.getElementById('question-category').textContent = question.category;
  
  // Update phase title
  const phaseInfo = assessmentParser.getPhaseInfo(currentPhase);
  document.getElementById('phase-title').textContent = `Phase ${currentPhase}: ${phaseInfo.name}`;
  
  // Update question counter
  const totalQuestions = phaseQuestions[currentPhase].length;
  document.getElementById('question-counter').textContent = `Pertanyaan ${currentQuestionIndex + 1} dari ${totalQuestions}`;
  
  // Load saved answer if exists
  const savedAnswer = answers[question.id];
  if (savedAnswer) {
    const radio = document.querySelector(`input[name="answer"][value="${savedAnswer}"]`);
    if (radio) radio.checked = true;
  } else {
    // Clear all radio buttons
    document.querySelectorAll('input[name="answer"]').forEach(radio => radio.checked = false);
  }
  
  // Update navigation buttons
  document.getElementById('prev-btn').disabled = currentQuestionIndex === 0 && currentPhase === 1;
  
  const isLastQuestion = currentPhase === 3 && currentQuestionIndex === phaseQuestions[3].length - 1;
  const nextBtn = document.getElementById('next-btn');
  nextBtn.textContent = isLastQuestion ? 'Selesai Assessment' : 'Selanjutnya →';
}

function getCurrentQuestion() {
  return phaseQuestions[currentPhase]?.[currentQuestionIndex];
}

function updateProgress() {
  // Update phase progress
  const currentPhaseQuestions = phaseQuestions[currentPhase];
  const phaseAnswers = Object.keys(answers).filter(key =>
    currentPhaseQuestions.some(q => q.id.toString() === key)
  ).length;
  const phaseProgress = (phaseAnswers / currentPhaseQuestions.length) * 100;
  
  document.getElementById('phase-progress-bar').style.width = `${phaseProgress}%`;
  document.getElementById('phase-progress-text').textContent = `${Math.round(phaseProgress)}%`;
  document.getElementById('phase-progress-label').textContent = `Phase ${currentPhase} Progress`;
  
  // Update total progress
  const totalQuestions = Object.values(phaseQuestions).reduce((sum, questions) => sum + questions.length, 0);
  const totalAnswers = Object.keys(answers).length;
  const totalProgress = totalQuestions > 0 ? (totalAnswers / totalQuestions) * 100 : 0;
  
  document.getElementById('total-progress').style.width = `${totalProgress}%`;
  document.getElementById('total-progress-text').textContent = `${Math.round(totalProgress)}% Complete`;
  
  // Rebuild sidebar to update progress
  buildSidebarNavigation();
}

// Export functions for global access
export function nextQuestion() {
  const selectedAnswer = document.querySelector('input[name="answer"]:checked');
  if (!selectedAnswer) {
    alert('Silakan pilih jawaban terlebih dahulu');
    return;
  }
  
  // Save answer
  const question = getCurrentQuestion();
  answers[question.id] = parseInt(selectedAnswer.value);
  localStorage.setItem('assessment3PhaseAnswers', JSON.stringify(answers));
  
  // Move to next question
  if (currentQuestionIndex < phaseQuestions[currentPhase].length - 1) {
    currentQuestionIndex++;
  } else if (currentPhase < 3) {
    // Move to next phase
    currentPhase++;
    currentQuestionIndex = 0;
  } else {
    // Assessment completed
    completeAssessment();
    return;
  }
  
  loadCurrentQuestion();
  updateProgress();
}

export function previousQuestion() {
  if (currentQuestionIndex > 0) {
    currentQuestionIndex--;
  } else if (currentPhase > 1) {
    // Move to previous phase
    currentPhase--;
    currentQuestionIndex = phaseQuestions[currentPhase].length - 1;
  }
  
  loadCurrentQuestion();
  updateProgress();
}

export function skipQuestion() {
  // Move to next question without saving answer
  if (currentQuestionIndex < phaseQuestions[currentPhase].length - 1) {
    currentQuestionIndex++;
  } else if (currentPhase < 3) {
    currentPhase++;
    currentQuestionIndex = 0;
  } else {
    completeAssessment();
    return;
  }
  
  loadCurrentQuestion();
  updateProgress();
}

export function jumpToPhase(phaseNum) {
  currentPhase = parseInt(phaseNum);
  currentQuestionIndex = 0;
  loadCurrentQuestion();
  updateProgress();
}

export function jumpToCategory(categoryName) {
  const questions = phaseQuestions[currentPhase];
  const categoryIndex = questions.findIndex(q => q.category === categoryName);
  if (categoryIndex !== -1) {
    currentQuestionIndex = categoryIndex;
    loadCurrentQuestion();
    updateProgress();
  }
}

export function handleAnswerChange(value) {
  // Enable next button when answer is selected
  document.getElementById('next-btn').disabled = false;
}

export function saveAndExit() {
  localStorage.setItem('assessment3PhaseAnswers', JSON.stringify(answers));
  localStorage.setItem('assessment3PhaseState', JSON.stringify({
    currentPhase,
    currentQuestionIndex
  }));
  navigateTo('dashboard');
}

async function completeAssessment() {
  try {
    // Save answers to localStorage
    localStorage.setItem('assessment3PhaseAnswers', JSON.stringify(answers));
    localStorage.setItem('assessment3PhaseCompleted', 'true');

    // Validate completeness
    const validation = assessmentScoring.validateCompleteness(answers);
    console.log('Assessment validation:', validation);

    if (!validation.isComplete) {
      const proceed = confirm(
        `Assessment belum lengkap (${validation.completionPercentage}% selesai). ` +
        `Apakah Anda ingin melanjutkan dengan hasil yang ada?`
      );

      if (!proceed) {
        return; // Stay on current question
      }
    }

    // Calculate assessment results
    const assessmentResults = assessmentScoring.calculateAssessmentResults(answers);
    console.log('Calculated assessment results:', assessmentResults);

    // Submit assessment for AI processing
    const saveResult = await assessmentService.submitAssessment(assessmentResults);
    console.log('Submit result:', saveResult);

    if (saveResult.success) {
      localStorage.setItem('assessmentResultReady', 'true');
      localStorage.setItem('lastSaveResult', JSON.stringify(saveResult));
    } else {
      // Still proceed even if save failed (fallback was used)
      localStorage.setItem('assessmentResultReady', 'true');
      localStorage.setItem('lastSaveResult', JSON.stringify(saveResult));
    }

    // Navigate to waiting page
    navigateTo('waiting');

  } catch (error) {
    console.error('Error completing assessment:', error);
    alert('Terjadi kesalahan saat menyimpan hasil assessment. Silakan coba lagi.');
  }
}